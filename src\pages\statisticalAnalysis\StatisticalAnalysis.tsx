import React, { useState } from 'react';
import { Card, Row, Col, DatePicker, Select, Button } from 'yth-ui';
import dayjs from 'dayjs';
import styles from './index.module.less';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface StatisticsCardProps {
  title: string;
  value: string | number;
  unit?: string;
  icon?: React.ReactNode;
  color?: string;
}

// 统计卡片组件
const StatisticsCard: React.FC<StatisticsCardProps> = ({ 
  title, 
  value, 
  unit = '', 
  icon, 
  color = '#1890ff' 
}) => {
  return (
    <Card className={styles.statisticsCard}>
      <div className={styles.cardContent}>
        <div className={styles.cardIcon} style={{ backgroundColor: color }}>
          {icon || <span>📊</span>}
        </div>
        <div className={styles.cardInfo}>
          <div className={styles.cardTitle}>{title}</div>
          <div className={styles.cardValue}>
            {value}
            {unit && <span className={styles.cardUnit}>{unit}</span>}
          </div>
        </div>
      </div>
    </Card>
  );
};

const StatisticalAnalysis: React.FC = () => {
  // 筛选条件状态
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [deviceType, setDeviceType] = useState<string>('all');
  const [region, setRegion] = useState<string>('all');

  // 模拟统计数据
  const statisticsData = [
    {
      title: '总用电量',
      value: '12,345',
      unit: 'kWh',
      icon: '⚡',
      color: '#52c41a'
    },
    {
      title: '设备数量',
      value: '156',
      unit: '台',
      icon: '🔧',
      color: '#1890ff'
    },
    {
      title: '能耗成本',
      value: '8,976',
      unit: '元',
      icon: '💰',
      color: '#faad14'
    },
    {
      title: '节能率',
      value: '15.6',
      unit: '%',
      icon: '🌱',
      color: '#13c2c2'
    }
  ];

  // 处理筛选条件重置
  const handleReset = () => {
    setDateRange(null);
    setDeviceType('all');
    setRegion('all');
  };

  // 处理查询
  const handleSearch = () => {
    console.log('查询条件:', {
      dateRange,
      deviceType,
      region
    });
    // 这里可以添加实际的查询逻辑
  };

  return (
    <div className={styles.statisticalAnalysis}>
      {/* 第一个模块：筛选条件 */}
      <Card className={styles.filterSection} title="筛选条件">
        <Row gutter={[16, 16]} align="middle">
          <Col span={6}>
            <div className={styles.filterItem}>
              <label className={styles.filterLabel}>时间范围：</label>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                placeholder={['开始日期', '结束日期']}
                style={{ width: '100%' }}
              />
            </div>
          </Col>
          <Col span={4}>
            <div className={styles.filterItem}>
              <label className={styles.filterLabel}>设备类型：</label>
              <Select
                value={deviceType}
                onChange={setDeviceType}
                style={{ width: '100%' }}
                placeholder="请选择设备类型"
              >
                <Option value="all">全部</Option>
                <Option value="air_conditioner">空调</Option>
                <Option value="lighting">照明</Option>
                <Option value="elevator">电梯</Option>
                <Option value="other">其他</Option>
              </Select>
            </div>
          </Col>
          <Col span={4}>
            <div className={styles.filterItem}>
              <label className={styles.filterLabel}>区域：</label>
              <Select
                value={region}
                onChange={setRegion}
                style={{ width: '100%' }}
                placeholder="请选择区域"
              >
                <Option value="all">全部</Option>
                <Option value="building_a">A栋</Option>
                <Option value="building_b">B栋</Option>
                <Option value="building_c">C栋</Option>
                <Option value="parking">停车场</Option>
              </Select>
            </div>
          </Col>
          <Col span={6}>
            <div className={styles.filterActions}>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginLeft: 8 }}>
                重置
              </Button>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 第二个模块：四个统计数字卡片 */}
      <div className={styles.statisticsSection}>
        <Row gutter={[16, 16]}>
          {statisticsData.map((item, index) => (
            <Col span={6} key={index}>
              <StatisticsCard
                title={item.title}
                value={item.value}
                unit={item.unit}
                icon={item.icon}
                color={item.color}
              />
            </Col>
          ))}
        </Row>
      </div>

      {/* 第三个模块：暂时不写（预留空间） */}
      <Card className={styles.chartSection} title="图表分析">
        <div className={styles.placeholder}>
          <div className={styles.placeholderIcon}>📈</div>
          <div className={styles.placeholderText}>图表模块暂未开发</div>
          <div className={styles.placeholderSubtext}>此区域将用于显示各种数据图表和分析</div>
        </div>
      </Card>
    </div>
  );
};

export default StatisticalAnalysis;
